use crate::{ Element, TypedProps, VirtualNode};
use std::any::TypeId;
use std::collections::HashMap;
use std::sync::{Mutex, OnceLock};

/// Information about a widget type
#[derive(Debug, Clone)]
pub struct WidgetInfo {
    pub name: String,
    pub props_type_id: TypeId,
}

/// A factory function for creating widget elements
pub type WidgetFactory = Box<dyn Fn(&TypedProps, Vec<Element>) -> Element + Send + Sync>;

/// Registry for widget types and their associated props
pub struct WidgetRegistry {
    widgets: HashMap<String, WidgetInfo>,
    factories: HashMap<String, WidgetFactory>,
}

impl WidgetRegistry {
    pub fn new() -> Self {
        Self {
            widgets: HashMap::new(),
            factories: HashMap::new(),
        }
    }

    /// Register a widget with its props type and factory function
    pub fn register_widget<P>(&mut self, name: impl Into<String>, factory: WidgetFactory)
    where
        P:  'static,
    {
        let name = name.into();
        let widget_info = WidgetInfo {
            name: name.clone(),
            props_type_id: TypeId::of::<P>(),
        };

        self.widgets.insert(name.clone(), widget_info);
        self.factories.insert(name, factory);
    }

    /// Get widget information by name
    pub fn get_widget_info(&self, name: &str) -> Option<&WidgetInfo> {
        self.widgets.get(name)
    }

    /// Check if a widget is registered
    pub fn is_widget(&self, name: &str) -> bool {
        self.widgets.contains_key(name)
    }

    /// Create a widget element using the registered factory
    pub fn create_widget(
        &self,
        name: &str,
        props: TypedProps,
        children: Vec<Element>,
    ) -> Option<Element> {
        self.factories
            .get(name)
            .map(|factory| factory(&props, children))
    }

    /// Get all registered widget names
    pub fn widget_names(&self) -> Vec<String> {
        self.widgets.keys().cloned().collect()
    }

    /// Create a default registry with built-in widgets
    pub fn with_builtin_widgets() -> Self {
        let mut registry = Self::new();

        // Register Block widget
        registry.register_widget::<crate::BlockProps>(
            "Block",
            Box::new(|props, children| {
                VirtualNode::widget(crate::WidgetType::Block, props.clone(), children)
            }),
        );

        // Register Text widget
        registry.register_widget::<crate::TextProps>(
            "Text",
            Box::new(|props, children| {
                VirtualNode::widget(crate::WidgetType::Text, props.clone(), children)
            }),
        );

        // Register Layout widget
        registry.register_widget::<crate::LayoutProps>(
            "Layout",
            Box::new(|props, children| {
                VirtualNode::widget(crate::WidgetType::Layout, props.clone(), children)
            }),
        );

        registry
    }
}

impl Default for WidgetRegistry {
    fn default() -> Self {
        Self::with_builtin_widgets()
    }
}

/// Global widget registry instance
static WIDGET_REGISTRY: OnceLock<Mutex<WidgetRegistry>> = OnceLock::new();

/// Get the global widget registry
pub fn get_widget_registry() -> &'static Mutex<WidgetRegistry> {
    WIDGET_REGISTRY.get_or_init(|| Mutex::new(WidgetRegistry::with_builtin_widgets()))
}

/// Initialize the global widget registry with custom widgets
pub fn init_widget_registry(registry: WidgetRegistry) {
    let _ = WIDGET_REGISTRY.set(Mutex::new(registry));
}
