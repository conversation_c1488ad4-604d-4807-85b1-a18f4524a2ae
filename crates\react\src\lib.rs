use crossterm::{
    event::{self, DisableMouseCapture, EnableMouseCapture, Event, KeyCode},
    execute,
    terminal::{EnterAlternateScreen, LeaveAlternateScreen, disable_raw_mode, enable_raw_mode},
};
use ratatui::{Terminal, backend::CrosstermBackend};
use std::rc::Rc;
use std::time::Duration;
use std::{io::stdout, sync::Arc};

// Re-export core types and macros
pub use terminus_ui_component_macro::component;
pub use terminus_ui_core::hooks::{StateHandle, StateSetter, set_current_event_context, use_event};
pub use terminus_ui_core::*;
pub use terminus_ui_props_macro::Props;
pub use terminus_ui_rsx_macro::rsx;

// Components module
pub mod components;

/// Prelude module for common imports
pub mod prelude {
    pub use crate::components::{
        Dialog, DialogContent, DialogContentProps, DialogProps, DialogTitle, DialogTitleProps,
        ScrollArea, ScrollAreaProps, ScrollBar, ScrollBarProps, ScrollOrientation,
        ScrollbarPosition,
    };
    pub use crate::*;
    pub use ratatui::{
        DefaultTerminal, Frame, Terminal, backend::*, layout::*, style::*, text::*, widgets::*,
    };
}

/// Render an element to the terminal (synchronous)
///
/// This function provides synchronous rendering for applications that don't need async capabilities.
/// For async applications, consider using [`render_async`] instead.
///
/// # Example
/// ```rust,no_run
/// use terminus_ui::prelude::*;
///
/// let element = rsx! {
///     <Text content="Hello, World!" />
/// };
///
/// render(element)?;
/// # Ok::<(), Box<dyn std::error::Error>>(())
/// ```
pub fn render(element: Element) -> Result<(), Box<dyn std::error::Error>> {
    render_to_terminal(element)
}

/// Render an element to the terminal asynchronously with Tokio
///
/// This function provides non-blocking, asynchronous rendering capabilities for the terminus-ui framework.
/// It's specifically designed to work with the Tokio async runtime for optimal performance and reliability.
///
/// # Features
///
/// - **Non-blocking**: Allows other async tasks to run concurrently while the UI is being rendered
/// - **Tokio Optimized**: Uses Tokio's spawn_blocking and yield_now for optimal async performance
/// - **Proper Resource Management**: Ensures terminal cleanup even if the async task is cancelled
/// - **Thread Safe**: Uses `Send + Sync` error types for safe use across async contexts
/// - **Performance Optimized**: Minimizes context switching and maintains good async performance
///
/// # When to Use
///
/// Use `render_async` when:
/// - Your application uses Tokio and async/await patterns
/// - You need to handle UI rendering alongside other async operations (network requests, file I/O, etc.)
/// - You want non-blocking UI updates
/// - Your application architecture is built around Tokio
///
/// Use the synchronous [`render`] function when:
/// - Your application is primarily synchronous
/// - You have simple, blocking UI requirements
/// - You don't need concurrent async operations
///
/// # Runtime Requirements
///
/// This function requires the Tokio async runtime to be available:
/// - **Tokio**: `#[tokio::main]` or `tokio::runtime::Runtime`
/// - Add `tokio = { version = "1.0", features = ["full"] }` to your Cargo.toml
///
/// # Examples
///
/// ## Basic Usage with Tokio
/// ```rust,no_run
/// use terminus_ui::prelude::*;
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
///     let element = rsx! {
///         <Text content="Hello, Async World!" />
///     };
///
///     render_async(element).await?;
///     Ok(())
/// }
/// ```
///
/// ## Concurrent Operations
/// ```rust,no_run
/// use terminus_ui::prelude::*;
/// use tokio::time::{sleep, Duration};
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
///     let ui_task = async {
///         let element = rsx! {
///             <Text content="Rendering while other tasks run..." />
///         };
///         render_async(element).await
///     };
///
///     let background_task = async {
///         loop {
///             println!("Background task running...");
///             sleep(Duration::from_secs(1)).await;
///         }
///     };
///
///     // Run UI and background tasks concurrently
///     tokio::select! {
///         result = ui_task => result?,
///         _ = background_task => {},
///     }
///
///     Ok(())
/// }
/// ```
///
/// ## With Custom Tokio Runtime
/// ```rust,no_run
/// use terminus_ui::prelude::*;
/// use tokio::runtime::Runtime;
///
/// fn main() -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
///     let rt = Runtime::new()?;
///     rt.block_on(async {
///         let element = rsx! {
///             <Text content="Hello from custom Tokio runtime!" />
///         };
///
///         render_async(element).await
///     })
/// }
/// ```
///
/// # Error Handling
///
/// The function returns errors that implement `Send + Sync` for thread safety in async contexts.
/// Common error scenarios include:
/// - Terminal initialization failures
/// - I/O errors during rendering
/// - Event handling errors
///
/// # Cancellation Safety
///
/// This function is cancellation-safe. If the async task is cancelled or dropped:
/// - Terminal state will be properly restored
/// - Raw mode will be disabled
/// - Screen buffers will be cleaned up
/// - No resource leaks will occur
///
/// # Performance Considerations
///
/// - Uses efficient async I/O operations
/// - Minimizes blocking operations
/// - Yields control to the async runtime regularly
/// - Optimized for low-latency UI updates
///
/// # Thread Safety
///
/// While the function itself is not `Send` (due to terminal access), the error types
/// are `Send + Sync` to ensure proper error propagation in async contexts.
pub async fn render_async(
    element: Element,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    render_to_terminal_async(element).await
}

/// Render an element to the terminal with full setup
pub fn render_to_terminal(element: Element) -> Result<(), Box<dyn std::error::Error>> {
    // Setup terminal
    enable_raw_mode()?;
    let mut stdout = stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend)?;

    // Create hook context
    let hook_context = Rc::new(HookContext::new());
    set_hook_context(hook_context.clone());

    // Main render loop
    loop {
        // Reset hooks for new render cycle
        hook_context.reset();

        // Render the element
        terminal.draw(|f| {
            let area = f.area();
            render_element_to_frame(&element, area, f);
        })?;

        // Handle events
        if event::poll(std::time::Duration::from_millis(100))? {
            let event = event::read()?;
            set_current_event_context(Some(Arc::new(event.clone())));
            if let Event::Key(key) = event {
                if let KeyCode::Char('q') = key.code {
                    break;
                }
            }
        }
    }

    // Cleanup
    clear_hook_context();
    disable_raw_mode()?;
    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    terminal.show_cursor()?;

    Ok(())
}

/// Render an element to the terminal with full async setup
///
/// This is the core async rendering implementation that provides non-blocking terminal rendering.
/// It includes proper resource management, cancellation safety, and async-friendly event handling.
pub async fn render_to_terminal_async(
    element: Element,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // Setup terminal - these operations are fast and don't need to be async
    enable_raw_mode().map_err(AsyncRenderError::from_error)?;
    let mut stdout = stdout();
    execute!(stdout, EnterAlternateScreen, EnableMouseCapture)
        .map_err(AsyncRenderError::from_error)?;
    let backend = CrosstermBackend::new(stdout);
    let mut terminal = Terminal::new(backend).map_err(AsyncRenderError::from_error)?;

    // Create hook context
    let hook_context = Rc::new(HookContext::new());
    set_hook_context(hook_context.clone());

    // Async render loop with proper resource cleanup
    let result = async_render_loop(&mut terminal, &element, &hook_context).await;

    // Cleanup - ensure this happens even if the async task is cancelled
    cleanup_terminal_async(&mut terminal)
        .await
        .map_err(AsyncRenderError::from_boxed)?;
    clear_hook_context();

    // Return the result from the render loop
    result.map_err(AsyncRenderError::from_boxed)
}

/// Main async render loop that yields control to the async runtime
async fn async_render_loop(
    terminal: &mut Terminal<CrosstermBackend<std::io::Stdout>>,
    element: &Element,
    hook_context: &Rc<HookContext>,
) -> Result<(), Box<dyn std::error::Error>> {
    loop {
        // Reset hooks for new render cycle
        hook_context.reset();

        // Render the element
        terminal.draw(|f| {
            let area = f.area();
            render_element_to_frame(element, area, f);
        })?;

        // Async event handling - this is where we yield control to the runtime
        if async_poll_event_tokio(Duration::from_millis(100)).await? {
            let event = async_read_event_tokio().await?;
            let event_arc = Arc::new(event.clone());
            set_current_event_context(Some(event_arc));
            if let Event::Key(key) = event {
                if let KeyCode::Char('q') = key.code {
                    break;
                }
            }
        }

        // Yield control to the async runtime to allow other tasks to run
        yield_now_tokio().await;
    }

    Ok(())
}

/// Tokio-specific async event polling that doesn't block the runtime
async fn async_poll_event_tokio(timeout: Duration) -> Result<bool, Box<dyn std::error::Error>> {
    // Use tokio's spawn_blocking for potentially blocking operations
    let result = spawn_blocking_tokio(move || event::poll(timeout)).await?;

    Ok(result?)
}

/// Tokio-specific async event reading
async fn async_read_event_tokio() -> Result<Event, Box<dyn std::error::Error>> {
    let result = spawn_blocking_tokio(event::read).await?;

    Ok(result?)
}

/// Tokio-specific spawn_blocking implementation
async fn spawn_blocking_tokio<F, R>(f: F) -> Result<R, Box<dyn std::error::Error>>
where
    F: FnOnce() -> R + Send + 'static,
    R: Send + 'static,
{
    // Use tokio's spawn_blocking for optimal async performance
    Ok(tokio::task::spawn_blocking(f).await?)
}

/// Tokio-specific yield implementation
async fn yield_now_tokio() {
    // Use tokio's yield_now for optimal async performance
    tokio::task::yield_now().await;
}

/// Async terminal cleanup that ensures proper resource management
async fn cleanup_terminal_async(
    terminal: &mut Terminal<CrosstermBackend<std::io::Stdout>>,
) -> Result<(), Box<dyn std::error::Error>> {
    // These cleanup operations are typically fast, but we make them async-friendly
    // by yielding control after each operation

    disable_raw_mode()?;
    yield_now_tokio().await;

    execute!(
        terminal.backend_mut(),
        LeaveAlternateScreen,
        DisableMouseCapture
    )?;
    yield_now_tokio().await;

    terminal.show_cursor()?;
    yield_now_tokio().await;

    Ok(())
}

/// Custom error type that implements Send + Sync for async contexts
#[derive(Debug)]
struct AsyncRenderError {
    message: String,
    source: Option<Box<dyn std::error::Error + Send + Sync>>,
}

impl std::fmt::Display for AsyncRenderError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Async render error: {}", self.message)
    }
}

impl std::error::Error for AsyncRenderError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        self.source
            .as_ref()
            .map(|e| e.as_ref() as &(dyn std::error::Error + 'static))
    }
}

impl AsyncRenderError {
    /// Create from a concrete error type
    fn from_error<E: std::error::Error + 'static>(
        error: E,
    ) -> Box<dyn std::error::Error + Send + Sync> {
        Box::new(AsyncRenderError {
            message: error.to_string(),
            source: None,
        })
    }

    /// Create from a boxed error
    fn from_boxed(error: Box<dyn std::error::Error>) -> Box<dyn std::error::Error + Send + Sync> {
        Box::new(AsyncRenderError {
            message: error.to_string(),
            source: None,
        })
    }
}

/// Render an element to a ratatui frame
fn render_element_to_frame(
    element: &Element,
    area: ratatui::layout::Rect,
    frame: &mut ratatui::Frame,
) {
    widgets::render_virtual_node(element, area, frame);
}
