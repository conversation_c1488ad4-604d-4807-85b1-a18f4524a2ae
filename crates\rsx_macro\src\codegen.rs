use crate::parsing::{RsxElement, RsxProp, RsxPropValue};
use quote::{quote, quote_spanned};

/// RSX code generation engine with optimization support
pub struct RsxCodeGenerator;

impl RsxCodeGenerator {
    /// Generate code with specific context and optimization level
    pub fn generate(element: &RsxElement) -> proc_macro2::TokenStream {
        match element {
            RsxElement::Text { content, .. } => quote! {
                ::terminus_ui::IntoElement::into_element(#content)
            },
            RsxElement::Expression { expr, .. } => quote! {
                ::terminus_ui::IntoElement::into_element(#expr)
            },
            RsxElement::Element {
                name,
                props,
                children,
                ..
            } => {
                // Use analysis to optimize children generation
                let children_code = Self::generate_children_code_with_analysis(children);

                // Get the element name as a string
                let name_str = if name.segments.len() == 1 {
                    name.segments.first().unwrap().ident.to_string()
                } else {
                    quote! { #name }.to_string()
                };

                // Check if this is a built-in widget or custom component
                if let Some((widget_type, props_type)) = Self::get_widget_type(&name_str) {
                    Self::generate_widget_code_with_analysis(
                        &widget_type,
                        &props_type,
                        props,
                        children_code,
                    )
                } else {
                    Self::generate_component_code_with_analysis(
                        &name_str,
                        props,
                        children,
                        children_code,
                    )
                }
            }
        }
    }

    /// Generate code for built-in widgets with analysis-driven optimization
    fn generate_widget_code_with_analysis(
        widget_type: &proc_macro2::TokenStream,
        props_type: &proc_macro2::TokenStream,
        props: &[RsxProp],
        children_code: proc_macro2::TokenStream,
    ) -> proc_macro2::TokenStream {
        let props_code = Self::generate_widget_props_code_with_analysis(props, props_type);

        quote! {
            VirtualNode::widget(
                #widget_type,
                #props_code,
                #children_code
            )
        }
    }

    /// Generate code for widget props using typed props structs
    fn generate_widget_props_code(
        props: &[RsxProp],
        props_type: &proc_macro2::TokenStream,
    ) -> proc_macro2::TokenStream {
        if props.is_empty() {
            quote! { #props_type::default() }
        } else {
            let prop_assignments = props.iter().map(|prop| {
                let name = prop.name.clone();
                match &prop.value {
                    RsxPropValue::Literal { value, .. } => {
                        // For string literals, check if the field expects Option<String> or String
                        match prop.name.to_string().as_str() {
                            "content" => quote! { #name: #value.to_string() }, // TextProps.content is String
                            _ => quote! { #name: Some(#value.to_string()) }, // Most other fields are Option<T>
                        }
                    }
                    RsxPropValue::Expression { expr, .. } => {
                        // For expressions, check if the field expects Option<T> or T
                        // Also handle callback props specially
                        match prop.name.to_string().as_str() {
                            "content" => quote! { #name: #expr.to_string() }, // TextProps.content is String
                            name if name.starts_with("on_") => {
                                // Callback props - use a helper that handles both Option<Callback> and Callback
                                quote! { #name: ::terminus_ui::IntoCallbackProp::into_callback_prop(#expr) }
                            }
                            _ => quote! { #name: Some(#expr) }, // Most other fields are Option<T>
                        }
                    }
                }
            });

            // Determine if we need ..Default::default() based on the props type and provided props
            let prop_names: std::collections::HashSet<String> =
                props.iter().map(|p| p.name.to_string()).collect();
            let needs_default = Self::needs_default_props(props_type, &prop_names);

            if needs_default {
                quote! {
                    #props_type {
                        #(#prop_assignments),*,
                        ..Default::default()
                    }
                }
            } else {
                quote! {
                    #props_type {
                        #(#prop_assignments),*
                    }
                }
            }
        }
    }

    /// Generate code for custom component props using Yew's exact approach
    fn generate_custom_component_props_code(
        props: &[RsxProp],
        component_name: &str,
    ) -> proc_macro2::TokenStream {
        let component_ident = syn::Ident::new(component_name, proc_macro2::Span::call_site());
        let component_span = proc_macro2::Span::call_site();

        // Use Yew's exact pattern: qualified path in type annotation context (which works!)
        let props_ty = quote_spanned!(component_span=> <#component_ident as ::terminus_ui::FunctionalComponent>::Properties);

        // Yew's validation pattern (works because it's in function parameter context)
        let props_ident = syn::Ident::new("__terminus_props", component_span);
        let check_props: proc_macro2::TokenStream = props
            .iter()
            .map(|prop| {
                let prop_name = prop.name.clone();
                quote_spanned! {component_span=>
                    let _ = &__terminus_props.#prop_name;
                }
            })
            .collect();

        let validate_props = quote_spanned! {component_span=>
            #[allow(clippy::no_effect)]
            if false {
                let _ = |#props_ident: #props_ty| {
                    #check_props
                };
            };
        };

        // Yew's approach: use type annotation instead of struct initialization
        let build_props = if props.is_empty() {
            quote_spanned! {component_span=>
                {
                    // Use type annotation context (works!) instead of struct initialization
                    let props: #props_ty = Default::default();
                    props
                }
            }
        } else {
            // Generate individual field assignments
            let field_assignments = props.iter().map(|prop| {
                let name = prop.name.clone();
                let value = match &prop.value {
                    RsxPropValue::Literal { value, .. } => {
                        quote! { #value.to_string() }
                    }
                    RsxPropValue::Expression { expr, .. } => {
                        // Handle callback props specially
                        if prop.name.to_string().starts_with("on_") {
                            // Callback props - use helper that handles both Option<Callback> and Callback
                            quote! { ::terminus_ui::IntoCallbackProp::into_callback_prop(#expr) }
                        } else {
                            quote! { #expr }
                        }
                    }
                };
                quote! { props.#name = #value; }
            });

            quote_spanned! {component_span=>
                {
                    // Use type annotation context (works!) with Default and field updates
                    let mut props: #props_ty = Default::default();
                    #(#field_assignments)*
                    props
                }
            }
        };

        quote! {
            {
                #validate_props
                #build_props
            }
        }
    }

    /// Get widget type information for built-in widgets
    fn get_widget_type(name: &str) -> Option<(proc_macro2::TokenStream, proc_macro2::TokenStream)> {
        match name {
            "Block" => Some((
                quote! { ::terminus_ui::WidgetType::Block },
                quote! { ::terminus_ui::BlockProps },
            )),
            "Text" => Some((
                quote! { ::terminus_ui::WidgetType::Text },
                quote! { ::terminus_ui::TextProps },
            )),
            "Layout" => Some((
                quote! { ::terminus_ui::WidgetType::Layout },
                quote! { ::terminus_ui::LayoutProps },
            )),
            "Modal" => Some((
                quote! { ::terminus_ui::WidgetType::Modal },
                quote! { ::terminus_ui::ModalProps },
            )),
            "Scrollbar" => Some((
                quote! { ::terminus_ui::WidgetType::Scrollbar },
                quote! { ::terminus_ui::ScrollbarProps },
            )),
            _ => None,
        }
    }

    /// Check if default props are needed
    fn needs_default_props(
        props_type: &proc_macro2::TokenStream,
        prop_names: &std::collections::HashSet<String>,
    ) -> bool {
        match props_type.to_string().replace(" ", "").as_str() {
            "::terminus_ui::BlockProps" => {
                // BlockProps has: title, borders, border_style (all Option<T>)
                !(prop_names.contains("title")
                    && prop_names.contains("borders")
                    && prop_names.contains("border_style"))
            }
            "::terminus_ui::TextProps" => {
                // TextProps has: content (String), style (Option<Style>)
                !(prop_names.contains("content") && prop_names.contains("style"))
            }
            "::terminus_ui::LayoutProps" => {
                // LayoutProps has: direction, constraints, margin (all Option<T>)
                !(prop_names.contains("direction")
                    && prop_names.contains("constraints")
                    && prop_names.contains("margin"))
            }
            "::terminus_ui::ModalProps" => {
                // ModalProps has: open (bool), backdrop, center (Option<bool>)
                !(prop_names.contains("open")
                    && prop_names.contains("backdrop")
                    && prop_names.contains("center"))
            }
            "::terminus_ui::ScrollbarProps" => {
                // ScrollbarProps has many required fields, so we need all of them
                !(prop_names.contains("orientation")
                    && prop_names.contains("position")
                    && prop_names.contains("content_length")
                    && prop_names.contains("viewport_length")
                    && prop_names.contains("style")
                    && prop_names.contains("begin_symbol")
                    && prop_names.contains("end_symbol")
                    && prop_names.contains("thumb_symbol")
                    && prop_names.contains("track_symbol"))
            }
            _ => true, // For unknown types, always include default
        }
    }

    /// Generate code for custom components with analysis-driven optimization
    fn generate_component_code_with_analysis(
        name_str: &str,
        props: &[RsxProp],
        children: &[RsxElement],
        children_code: proc_macro2::TokenStream,
    ) -> proc_macro2::TokenStream {
        let component_name = syn::Ident::new(name_str, proc_macro2::Span::call_site());

        if children.is_empty() {
            // No children - use standard props generation
            let props_code =
                Self::generate_custom_component_props_code_with_analysis(props, name_str);

            quote! {
                #component_name::create_typed_element(#props_code)
            }
        } else {
            // Has children - try automatic children detection
            Self::generate_component_with_children_detection(
                &component_name,
                props,
                children,
                children_code,
                name_str,
            )
        }
    }

    /// Generate component code with automatic children detection
    fn generate_component_with_children_detection(
        component_name: &syn::Ident,
        props: &[RsxProp],
        _children: &[RsxElement],
        children_code: proc_macro2::TokenStream,
        name_str: &str,
    ) -> proc_macro2::TokenStream {
        // Generate props code that includes children handling
        let props_code = Self::generate_custom_component_props_code_with_children_support(
            props,
            children_code.clone(),
            name_str,
        );

        // Always use create_typed_element since children are handled in props generation
        quote! {
            #component_name::create_typed_element(#props_code)
        }
    }

    /// Generate custom component props code with automatic children support
    fn generate_custom_component_props_code_with_children_support(
        props: &[RsxProp],
        children_code: proc_macro2::TokenStream,
        component_name: &str,
    ) -> proc_macro2::TokenStream {
        let component_ident = syn::Ident::new(component_name, proc_macro2::Span::call_site());
        let component_span = proc_macro2::Span::call_site();

        // Use Yew's exact pattern: qualified path in type annotation context
        let props_ty = quote_spanned!(component_span=> <#component_ident as ::terminus_ui::FunctionalComponent>::Properties);

        // Generate individual field assignments for explicit props
        let field_assignments = props.iter().map(|prop| {
            let name = prop.name.clone();
            let value = match &prop.value {
                RsxPropValue::Literal { value, .. } => {
                    quote! { #value.to_string() }
                }
                RsxPropValue::Expression { expr, .. } => {
                    if prop.name.to_string().starts_with("on_") {
                        // Callback props - use helper that handles both Option<Callback> and Callback
                        quote! { ::terminus_ui::IntoCallbackProp::into_callback_prop(#expr) }
                    } else {
                        quote! { #expr }
                    }
                }
            };
            quote! { props.#name = #value; }
        });

        // Build props with automatic children detection
        quote_spanned! {component_span=>
            {
                // Create base props with provided properties
                let mut props: #props_ty = Default::default();

                // Set explicit props
                #(#field_assignments)*

                // Try to set children automatically if the component supports it
                let children_vec: Vec<::terminus_ui::Element> = #children_code;
                let children_obj = ::terminus_ui::Children::from_vec(children_vec);

                // Use a helper trait to try setting children
                props.try_set_children(children_obj);

                props
            }
        }
    }

    /// Generate code for widget props with analysis-driven optimization
    fn generate_widget_props_code_with_analysis(
        props: &[RsxProp],
        props_type: &proc_macro2::TokenStream,
    ) -> proc_macro2::TokenStream {
        Self::generate_widget_props_code(props, props_type)
    }

    /// Generate code for custom component props with analysis-driven optimization
    fn generate_custom_component_props_code_with_analysis(
        props: &[RsxProp],
        component_name: &str,
    ) -> proc_macro2::TokenStream {
        // Use the existing prop generation logic
        Self::generate_custom_component_props_code(props, component_name)
    }

    /// Generate code for children with analysis-driven optimization
    fn generate_children_code_with_analysis(children: &[RsxElement]) -> proc_macro2::TokenStream {
        if children.is_empty() {
            quote! { vec![] }
        } else {
            let child_codes = children.iter().map(Self::generate);
            quote! {
                vec![#(#child_codes),*]
            }
        }
    }
}
